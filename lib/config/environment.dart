import 'package:flutter_dotenv/flutter_dotenv.dart';

enum Environment { development, staging, production }

class EnvironmentConfig {
  static Environment _currentEnvironment = Environment.development;
  
  static Environment get currentEnvironment => _currentEnvironment;
  
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? 'http://localhost:3000';
  static String get appName => dotenv.env['APP_NAME'] ?? 'VNBDS';
  static bool get debugMode => dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';
  static String get environmentName => dotenv.env['ENVIRONMENT'] ?? 'development';
  
  static Future<void> initialize({Environment environment = Environment.development}) async {
    _currentEnvironment = environment;
    
    String envFile;
    switch (environment) {
      case Environment.development:
        envFile = '.env.development';
        break;
      case Environment.staging:
        envFile = '.env.staging';
        break;
      case Environment.production:
        envFile = '.env.production';
        break;
    }
    
    await dotenv.load(fileName: envFile);
  }
  
  static String getApiEndpoint(String path) {
    return '$apiBaseUrl$path';
  }
}
